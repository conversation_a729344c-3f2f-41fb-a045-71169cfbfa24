import { http } from "@/assets/js/axios_extend"
import {http_new} from '@/assets/js/axios_extend_new'
import { REQ_CONST_URL } from "@/assets/js/req_const_url"
/**
 * 【需求迭代】查询需求迭代列表
 * @param data
 * @returns {*}
 */
export function initQueryList (data) {
  return http.post(REQ_CONST_URL.DEMAND_MANAGE.QUERY_LIST, data)
}
/**
 * 【需求迭代】保存级联数据
 * @param data
 * @returns {*}
 */
export function saveCascade (data) {
  return http.post(REQ_CONST_URL.DEMAND_MANAGE.CASCADE_SAVE, data)
}
/**
 * 【需求迭代】查询模块下拉列表
 * @param data
 * @returns {*}
 */
export function queryCasadeList (data) {
  return http.post(REQ_CONST_URL.DEMAND_MANAGE.CASCADE_LIST, data)
}

/**
 * 【需求迭代】查询产品系统
 * @param {*} data
 * @returns
 */
export function querySystemList (data) {
  return http.post(REQ_CONST_URL.DEMAND_MANAGE.QUERY_SYSTEM_LIST, data)
}
/**
 * 【需求迭代】产品切换
 * @param {*} data
 * @returns
 */
export function productSwitch (data) {
  return http.post(REQ_CONST_URL.DEMAND_MANAGE.PRODUCT_SWITCH, data)
}
/**
 * 【需求迭代】查看详情
 * @param {*} data
 * @returns
 */
export function queryDemandDetail (data) {
  return http.post(REQ_CONST_URL.DEMAND_MANAGE.QUERY_DEMAND_DETAIL, data)
}
/**
 * 【需求迭代】保存
 * @param {*} data
 * @returns
 */
export function saveDemand (data) {
  return http.post(REQ_CONST_URL.DEMAND_MANAGE.SAVE_DEMAND, data)
}
/**
 * 【需求迭代】查询模块是否绑定了功能
 * @param {*} data
 * @returns
 */
export function queryIsBind (data) {
  return http.post(REQ_CONST_URL.DEMAND_MANAGE.QUERY_ISBIND, data)
}
/**
 * 【需求迭代】删除功能、模块
 * @param {*} data
 * @returns
 */
export function delDemandCascade (data) {
  return http.post(REQ_CONST_URL.DEMAND_MANAGE.DEL_DEMAND_CASCADE, data)
}
/**
 * 【需求迭代】查询级联数据详情
 * @param {*} data
 * @returns
 */
export function queryCascadeInfo (data) {
  return http.post(REQ_CONST_URL.DEMAND_MANAGE.CASCADE_INFO, data)
}
/**
 * 【需求迭代】查询异常需求列表
 * @param {*} data
 * @returns
 */
export function queryExcList (data) {
  return http.post(REQ_CONST_URL.DEMAND_MANAGE.EXC_DEMAND_LIST, data)
}
/**
 * 【需求迭代】保存异常原因
 * @param {*} data
 * @returns
 */
export function saveReason (data) {
  return http.post(REQ_CONST_URL.DEMAND_MANAGE.SAVE_REASON, data)
}

/**
 * 【需求-功能开关】查询项目下的功能开关数据
 * @param {*} data
 * @returns
 */
 export function queryProjectToggle (data) {
  return http_new.post(REQ_CONST_URL.DEMAND_MANAGE.QUERY_PROJECT_TOGGLE, data)
}
/**
* 【需求-功能开关】需求关联功能开关
* @param {*} data
* @returns
*/
export function relateToggle (data) {
  return http_new.post(REQ_CONST_URL.DEMAND_MANAGE.RELATE_TOGGLE, data)
}

// 创建开关
export function addFeatureToggle (data) {
  return http_new.post(REQ_CONST_URL.DEMAND_MANAGE.ADD_FEATURE_TOGGLE, data)
}
// 功能开关列表
export function queryFeatureToggleList (data) {
  return http_new.post(REQ_CONST_URL.DEMAND_MANAGE.FEATURE_TOGGLE_LIST, data)
}
// 发布版本前查询
export function queryPublishFeatureToggle (data) {
  return http_new.post(REQ_CONST_URL.DEMAND_MANAGE.QUERY_PUBLISH_FEATURE_TOGGLE, data)
}
// 发布版本
export function publishFeatureToggle (data) {
  return http_new.post(REQ_CONST_URL.DEMAND_MANAGE.PUBLISH_FEATURE_TOGGLE, data)
}
// 查询异常团队列表
export function queryExcTeamList (data) {
    return http_new.post(REQ_CONST_URL.DEMAND_MANAGE.QUERY_EXC_TEAM_LIST, data)
}
// 查询需求效果图设计人员
export function queryDemandUiUser (data) {
  return http_new.post(REQ_CONST_URL.DEMAND_MANAGE.QUERY_DEMAND_UI_USER, data)
}
// 保存需求效果图设计人员
export function saveDemandUiUser (data) {
  return http_new.post(REQ_CONST_URL.DEMAND_MANAGE.SAVE_DEMAND_UI_USER, data)
}
//保存需求策略时前置处理
export function saveDemandFront (data) {
  return http_new.post(REQ_CONST_URL.DEMAND_MANAGE.SAVE_DEMAND_FRONT, data)
}
// 修改客户信息项目Id
export function updateCustomerProject (data) {
  return http_new.post(REQ_CONST_URL.DEMAND_MANAGE.UPDATE_CUSTOMER_PROJECT, data)
}
