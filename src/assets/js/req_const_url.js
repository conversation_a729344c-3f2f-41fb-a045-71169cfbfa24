/*
 * @Descripttion:
 * @version:
 * @Author: caobo
 * @Date: 2021-07-20 09:09:44
 * @LastEditors: 周振方 <EMAIL>
 * @LastEditTime: 2025-02-17 14:01:46
 */
/**
 * 調用后台 content-path
 * @type {string}
 */
const isDevelopment = process.env.NODE_ENV === 'production'
let BASE_URL = '/eagle_system';
let BASE_URL_HAS = (!isDevelopment ? '/api' : '..') + BASE_URL

/**
 * 请求后端URL 路径
 */
let REQ_CONST_URL = {
  LOGIN: {
    LOGIN_OUT: BASE_URL + '/v1/logouts'
  },
  INDEX: {
    QUERY_THEME_CONFIG: BASE_URL + '/theme/queryThemeConfig',
    QUERY_THEME_BANNER: BASE_URL + '/theme/queryThemeBanner',
    QUERY_LOGIN_USER: BASE_URL + '/theme/queryUserInfo',
  },
  SPRINT: {
    QUERY_LPD_TEAM: BASE_URL + '/sprint/queryLpdTeam',
    QUERY_LPD_TEAM_USERS: BASE_URL + '/sprint/queryLpdTeamUsers',
    QUERY_EAGLE_SPRINT: BASE_URL + '/sprint/queryEagleSprint',
    QUERY_EAGLE_NEW_SPRINT: BASE_URL + '/sprint/new/queryEagleSprint',
    QUERY_LPD_USER_BY_SPRINT: BASE_URL + '/sprint/queryLpdUserBySprint',
    QUERY_LPD_DATA_USER_BY_SPRINT: BASE_URL + '/sprint/queryLpdDataUserBySprint',
    UPDATE_EAGLE_SPRINT_STATUS: BASE_URL + '/sprint/updateEagleSprintStatus',
    KEEP_SPRINT_JOIN_USER: BASE_URL + '/sprint/keepSprintJoinUser',
    KEEP_SPRINT_JOIN_DATA_USER: BASE_URL + '/sprint/keepSprintJoinDataUser',
    GET_SYSTEM_PARAMETER: BASE_URL + '/pub/api/sprint/getSystemParameter',
    ARCHIVE_CLICK: BASE_URL + '/dbdic/sprint/final',
    TASK_AWAIT_DEV: BASE_URL + '/awaitdev/update',
    TASK_QUERY: BASE_URL + '/awaitdev/query'
  },
  DADABASE: {
    QUERYTABLE: BASE_URL + '/dbdesign/sprint/queryTable',
    queryProdTable: BASE_URL + '/dbdesign/sprint/queryProdTable',
    queryTableSpace: BASE_URL + '/dbdesign/sprint/queryTableSpace',
    queryTableInfo: BASE_URL + '/dbdesign/sprint/queryTableInfo',
    addTable: BASE_URL + '/dbdesign/sprint/verifyTable',
    saveTable: BASE_URL + '/dbdesign/table/saveTable',
    edit: BASE_URL + '/dbdesign/table/modifyTableInfo',
    delete: BASE_URL + '/dbdesign/table/deleteTable',
    reviewTable: BASE_URL + '/dbdesign/sprint/reviewTable',
    finishDesign: BASE_URL + '/dbdesign/sprint/finishDesign',
    exportSQL: BASE_URL + '/dbdesign/sprint/exportSQL',
    queryFieldType: BASE_URL + '/dbdesign/sprint/queryFieldType',
    queryStatus: BASE_URL + '/dbdesign/sprint/queryStatus',
    COLUMN_CONFIG: BASE_URL + '/dbdesign/sprint/columnConfig',
    TABLEINDEXTYPE: BASE_URL + '/dbdesign/sprint/queryIndexType',
    CATALOG_LIST: BASE_URL + '/catalog/v1/query/catalog/list', // 【数据资产】查询数据类别（目录）
    ADD_CATALOG: BASE_URL + '/catalog/v1/add/catalog',  //【数据资产】 新增数据类别（目录）
    QUERY_DEFAULT_FIELD: BASE_URL + '/dbdesign/query/default/field/catalog',//【数据库设计】查询默认字段的默认数据类别以及数据级别
    CREATE_FIELD_BY_AI: BASE_URL + '/dbdesign/ai/addFields'
  },
  FEATURE: {
    ADD_FUNCTION_DESIGN_URL: BASE_URL_HAS + '/fun/addFunctionDesign',
    NEW_FUNCTION_DESIGN: BASE_URL + '/wps/office/fun/addFile',
    ADD_FUNCTION_DESIGN: BASE_URL + '/fun/addFunctionDesign',
    queryList: BASE_URL + '/fun/findFunctionDesignById',
    delFunctionDesign: BASE_URL + '/fun/delFunctionDesign',
    download: BASE_URL + '/fun/downloadFunDesign',
    PREVIEW_FILE: BASE_URL + '/fun/previewFile',
    ONLINE_WPS_FILE: BASE_URL + '/wps/office/onlineWpsFile',
    ONLINE_EDIT: BASE_URL + '/fun/onlineEdit',
    VIEW_FILE: BASE_URL + '/fun/office/view/file'
  },
  ANNIVERSARY: {
    CHECK: BASE_URL + '/anni/care/queryAnniIsShow',
    QUERY: BASE_URL + '/mob/anni/care/queryAnniInfo',
    CHANGESTATE: BASE_URL + '/mob/anni/care/updateAnniInfo'
  },
  TDD: {
    TDD_SEARCH: BASE_URL + '/dbdic/table/list',
    TDD_DBDIC_DETAIL: BASE_URL + '/dbdic/table/detail',
    TDD_DBDIC_HISTORY: BASE_URL + '/dbdic/history/detail',
    TDD_DBDIC_SAVE: BASE_URL + '/dbdic/field/save',
    TDD_DBDIC_TABLE: BASE_URL + '/dbdic/unaudit/table/list', //【数据库字典】查询未审核的表
    TDD_SEARCH_SPRINT: BASE_URL + '/dbdic/query/sprint/list'
  },
  DAPR: {
    BUSINESS_LIST: BASE_URL + '/project/assets/business',
    DEPARTMENTS_LIST: BASE_URL + '/project/assets/departments',
    DAPR_LIST: BASE_URL + '/project/assets/searchList',
    DAPR_DETAIL: BASE_URL + '/project/assets/queryProjectDetail',
    DAPR_DETAIL_EIDET: BASE_URL + '/project/assets/saveProjectDetail',
    COMPONENTS_LIST: BASE_URL + '/component/list',
    COMPONENTS_DETAIL: BASE_URL + '/component/detail',
    COMPONENTS_SAVE: BASE_URL + '/component/save',
    DECRYPT_PASSWORD: BASE_URL + '/component/decryptDES'
  },
  OPP: {
    OPP_LIST: BASE_URL + '/project/apply/audit/list',
    APPLY_LIST: BASE_URL + '/project/apply/list',
    OPP_NOTICE: BASE_URL + '/project/operate/doNotice',
    OPP_DETAIL: BASE_URL + '/project/apply/detail',
    OPP_AUFIT: BASE_URL + '/project/apply/audit',
    OPP_AUFIT_ADUIT: BASE_URL + '/project/apply/v2/audit',
    OPP_DOMAIN: BASE_URL + '/project/operate/submitDomain',
    OPP_CMDB: BASE_URL + '/project/operate/submitCmdb',
    OPP_CMDB_V2: BASE_URL + '/project/operate/v2/submitCmdb',
    OPP_COMPONENT: BASE_URL + '/project/operate/executeComponent',
    OPP_DELPROFILE: BASE_URL + '/project/apply/del/profile',
    OPP_MODULE: BASE_URL + '/project/operate/build',
    NGINX_CONFIG: BASE_URL + '/project/apply/nginx/config',
    NGINX_EDIT: BASE_URL + '/project/apply/nginx/edit',
    NGINX_DEPLOY: BASE_URL + '/project/apply/nginx/deploy',
    OPP_BUILD_RESULT: BASE_URL + '/project/operate/ob/build/result',
    UPDATE_APPLY_STATE: BASE_URL + '/project/apply/upd/apply/state'
  },
  APPLYFORSOURCES: {
    QUERY_LIST: BASE_URL + '/project/apply/history',
    QUERY_DETAIL: BASE_URL + '/project/apply/detail',
    QUERY_PREPARE: BASE_URL + '/project/apply/prepare',
    QUERY_COMMIT: BASE_URL + '/project/apply/commit',
    QUERY_COMMIT_INSTANCE: BASE_URL + '/project/apply/v2/commit',
    DEL_APPLY: BASE_URL + '/project/apply/del',
    APPLY_TYPE_LIST: BASE_URL + '/project/apply/resources',
    APPLY_DEPENDENCIES: BASE_URL + '/project/apply/dependencies',
    PROJECT_CONFIG: BASE_URL + '/project/apply/projectConfig',
    OPERATE_OFFLINE: BASE_URL + '/project/operate/offline',
    AGAIN_COMMIT: BASE_URL + '/project/apply/v2/again/commit'
  },
  ENUM: {
    QUERY_LIST: BASE_URL + '/dic/enum/types',
    ENUM_ADD: BASE_URL + '/dic/enum/type/create',
    ENUM_DETAIL: BASE_URL + '/dic/enum/type/detail',
    ENUM_UPDATE: BASE_URL + '/dic/enum/type/edit',
    ENUM_DELETE: BASE_URL + '/dic/enum/type/del',
    ENUM_TYPE_LIST: BASE_URL + '/dic/enum/types',
    ENUM_TYPE_SAVE: BASE_URL + '/dic/enum/save',
    ENUM_BATCH_IMPORT: BASE_URL + '/dic/enum/import',
    ENUM_DELETE_BYID: BASE_URL + '/dic/enum/delete'
  },
  BURIED_POINT: {
    QUERY_LIST: BASE_URL + '/db/table/list',
    DELETE: BASE_URL + '/db/table/delete',
    ADD: BASE_URL + '/db/table/create',
    DETAIL: BASE_URL + '/db/table/detail',
    SAVE: BASE_URL + '/db/table/field/save',
    HISTORY: BASE_URL + '/db/table/history'
  },
  //    漏洞管理
  BUGS: {
    BUGS_COUNT: BASE_URL + '/bug/count',
    COMPONENTS_BUG: BASE_URL + '/bug/dependency/list',
    WHITE_ACTION: BASE_URL + '/bug/dependency/white',
    SET_STABLE: BASE_URL + '/bug/dependency/stable',
    COMPONENT_BUG_DETAIL: BASE_URL + '/bug/dependency/detail',
    RELATED_PROJECT: BASE_URL + '/bug/dependency/project',
    BUGS_DETAIL_LIST: BASE_URL + '/bug/dependency/detail',
    BUG_LEVELS: BASE_URL + '/bug/levels',
    BUGS_EXPORT: BASE_URL + '/bug/export',
    //    项目漏洞
    PROJECT_LIST: BASE_URL + '/bug/project/list',
    PROJECT_EXPORT: BASE_URL + '/bug/project/export',
    PROJECT_DETAIL: BASE_URL + '/bug/project/detail',
    //    新增&修改漏洞
    ADD_UPDATE_BUG: BASE_URL + '/bug/addOrUpd',
    PROJECT_LIST_BY_WHITE: BASE_URL + '/bug/dependency/white/options',
    // 漏洞版本列表
    DEPENDENCY_VERSION_LIST: BASE_URL + '/bug/dependency/version',
    // 手动设置稳定版本
    MANUAL_SET_STABLE_VERSION: BASE_URL + '/bug/dependency/stableVersion',
    // 处理项目详情流程
    DEAL_PROJECT_DETAIL_FLOW: BASE_URL + '/bug/project/flow',
    // 测试报告文件上传
    TEST_REPORT_FILE_UPLOAD: BASE_URL_HAS + '/bug/project/flow',
    //【漏洞修复计划】列表-查询数据
    REPAIR_PLAN_LIST: BASE_URL + '/bug/repairPlan/list',
    //【漏洞修复计划】新增-搜索组件漏洞数据
    REPAIR_PLAN_ADD_SEARCH: BASE_URL + '/bug/repairPlan/add/searchDependency',
    //【漏洞修复计划】新增-保存漏洞修复计划
    REPAIR_PLAN_ADD_SAVE: BASE_URL + '/bug/repairPlan/add/save',
    //【漏洞修复计划】列表-查询团队状态
    REPAIR_PLAN_QUERY_TEAM: BASE_URL + '/bug/repairPlan/list/queryTeamState',
    //【漏洞修复计划】列表-编辑时查询详情
    REPAIR_PLAN_EDIT_QUERY_DETAIL: BASE_URL + '/bug/repairPlan/edit/queryDetail',
    //编辑-查询组件关联团队弹窗数据
    REPAIR_PLAN_EDIT_QUERY: BASE_URL + '/bug/repairPlan/edit/queryTeamProject',
    //编辑-保存组件关联团队数据
    REPAIR_PLAN_EDIT_SAVE_TEAM: BASE_URL + '/bug/repairPlan/edit/saveTeamProject',
    //【漏洞修复计划2.0】维护组件选项列表（选项&计划复用）
    REPAIR_OPTION_LIST: BASE_URL + '/bug/repairOption/list',
    //【漏洞修复计划2.0】新增计划：选项关联团队详情
    REPAIR_OPTION_TEAMS_LIST: BASE_URL + '/bug/repairOption/teams',
    //【漏洞修复计划2.0】新增维护组件选项
    REPAIR_OPTION_ELEMENT_ADD: BASE_URL + '/bug/repairOption/add',
    //【漏洞修复计划2.0】编辑维护组件选项
    REPAIR_OPTION_ELEMENT_EDIT: BASE_URL + '/bug/repairOption/edit',
    //【漏洞修复计划2.0】编辑维护组件选项回显
    REPAIR_OPTION_ELEMENT_VIEW: BASE_URL + '/bug/repairOption/view',
    //【漏洞修复计划2.0】组件选项关联项目
    REPAIR_OPTION_PROJECT: BASE_URL + '/bug/repairOption/projects',
    //【漏洞修复计划2.0】选项添加项目白名单
    REPAIR_OPTION_IGNORE_PROJECT: BASE_URL + '/bug/repairOption/ignoreProject',
    //【漏洞修复计划】项目修复计划列表-查询数据
    PROJECT_PLAN_LIST: BASE_URL + '/bug/repairPlan/project/list',
    //【漏洞修复计划】项目修复计划列表-导出数据
    PROJECT_PLAN_EXPORT: BASE_URL + '/bug/repairPlan/project/listExport',

    // >>>>>>>>>>>>>>>>>>>>>>>>>>组件漏洞管理v2>>>>>>>>>>>>>>>>>>>>>>>>>>>>
    // 漏洞统计
    BUG_V2_COUNT: BASE_URL + '/bugv2/count',
    // 当天增量漏洞数 指定安全小组
    BUG_V2_CURRENTBUGCOUNT: BASE_URL + '/bugv2/currentBugCount',
    // 漏洞列表
    BUG_V2_LIST: BASE_URL + '/bugv2/list',
    // 项目信息
    BUG_V2_PROJECT: BASE_URL + '/bugv2/project',
    // 所有项目
    BUG_V2_ALL_PROJECT: BASE_URL + '/bugv2/project/list',
    // 手动扫描
    BUG_V2_EXCUTEANALYSE: BASE_URL + '/bugv2/excuteAnalyse',
    // 处理
    BUG_V2_PROCESS: BASE_URL + '/bugv2/process',
    BUG_V2_LAST_DEPLOY_STATE: BASE_URL + '/bugv2/last/deploy/state',
    // 【组件管理】查询组件的使用项目
    LOGORY_SDK_PROJECTS: BASE_URL + '/logory/sdk/projects'

  },
  //  质量管理
  QUALITY: {
    // 严重性统计
    CODE_SERIOUS_COUNT: BASE_URL + '/quality/issue/count',
    // 规则列表
    RULE_LIST_BY_PAGE: BASE_URL + '/quality/rules/list',
    // 查询条件
    RULE_QUERY_CONDITIONS: BASE_URL + '/quality/conditions',
    // 设置为白名单
    SET_WHITE_LIST: BASE_URL + '/quality/rules/white',
    // 获取关联项目
    QUERY_RELATED_PROJECT: BASE_URL + '/quality/rules/projects',
    // 规则详情
    RULE_ITEM_DETAIL: BASE_URL + '/quality/rule/detail',
    // 项目列表
    PROJECT_LIST: BASE_URL + '/quality/project/list',
    // 项目详情页
    PROJECT_LIST_DETAIL: BASE_URL + '/quality/project/detail',
    // 查看文件源码
    QUERY_FILE_SOURCE_CODE: BASE_URL + '/quality/sources/lines',
    // 查看分支名
    CODE_PROJECT_BRANCH_LIST: BASE_URL + '/quality/project/branch'
  },
  H5SCANNING: {
    ADD_KEY_WORD: BASE_URL + '/md/scan/v1/add/scan/keyword',
    QUERY_KEY_WORD_LIST: BASE_URL + '/md/scan/v1/query/keyword/array',
    ADD_SCANNING: BASE_URL + '/md/scan/v1/execute/system',
    QUERY_SCANNING_OPERATOR: BASE_URL + '/md/scan/v1/query/operator',
    QUERY_SCANNING_LIST: BASE_URL + '/md/scan/v1/query/execute/record',
  },
  //创建分支
  BRANCH: {
    //查询迭代的分支信息
    QUERY_SPRINT_BRANCH_LIST: BASE_URL + '/sprint/branch/list',
    //删除分支
    DELETE_SPRINT_BRANCH: BASE_URL + '/sprint/branch/delete',
    //解绑分支
    UNBIND_SPRINT_BRANCH: BASE_URL + '/sprint/branch/unbind',
    //迭代下项目查询接口
    QUERY_SPRINT_PROJECT_LIST: BASE_URL + '/sprint/project/list',
    //查询项目进行中的迭代分支
    QUERY_SPRINT_PROJECT_BRANCHLIST: BASE_URL + '/sprint/project/branchlist',
    //创建分支接口
    CREATE_SPRINT_BRANCH: BASE_URL + '/sprint/branch/create',
    //同步GIT接口
    SYNC_SPRINT_BRANCH: BASE_URL + '/sprint/branch/sync',
    //查询迭代分支详情接口
    QUERY_SPRINT_BRANCH_DETAIL: BASE_URL + '/sprint/branch/detail',
    //填充修复计划数据
    REPAIR_PLAN_FILL_DATA: BASE_URL + '/sprint/branch/repairPlan/fillData'
  },
  METADATA: {
    DISPATCH: BASE_URL + '/metadata/dispatch'
  },
  // 测试相关api
  TEST_PLATFORM: {
    // 查询数结构
    GROUP_TREE: BASE_URL + '/group/v1/oneGroupTree',
    // 用例列表数据
    CASE_LIST: BASE_URL + '/use/v1/findCaseListByGroup',
    // 选择分组模式
    GROUPING_MODE: BASE_URL + '/group/v1/saveGroupIterate',
    // 判断是否选择过分组模式
    IS_CHECKED_MODE: BASE_URL + '/group/v1/checkGroupIterate',
    // 新增用例保存
    SAVE_NEW_CASE: BASE_URL + '/use/v1/saveCase',
    // 用例详情
    CASE_DETAIL_INFO: BASE_URL + '/use/v1/findUseDatils',
    // 查询最近用例
    RELATED_CASE: BASE_URL + '/use/v1/findRecentUseList',
    // 查询创建人/测试人
    CREATOR_TESTER_LIST: BASE_URL + '/use/v1/iterateJoinUser',
    // 新建分组 选择关联项目
    GROUP_RELATED_PROJECT: BASE_URL + '/group/v1/chooseTeamProjectList',
    // 新建分组保存
    SAVE_GROUP_NAME: BASE_URL + '/group/v1/saveGroupTree',
    // 删除分组
    DELETE_GROUP: BASE_URL + '/group/v1/deleteGroupTree',
    //封存分组
    FENGCUN_GROUP: BASE_URL + '/pool/archive/group',
    // 用例步骤保存
    SAVE_CASE_STEP: BASE_URL + '/use/v1/addStep',
    // 用例步骤删除
    DELETE_CASE_STEP: BASE_URL + '/use/v1/deleteStep',
    // 查询是否是迭代人员
    QUERY_IS_ITERATE: BASE_URL + '/group/v1/checkIterateAuth',
    // 重命名分组
    RENAME_GROUP_NAME: BASE_URL + '/group/v1/upd/groupName',
    // 移动分组
    MOVE_GROUP_LOCATION: BASE_URL + '/group/v1/move/group',
    // 批量删除用例
    BATCH_DEL_CASE: BASE_URL + '/use/v1/batch/del/useCase',
    // 批量移动用例
    BATCH_MOVE_CASE: BASE_URL + '/use/v1/batch/move/useCase',
    // 用例字段更改状态
    CASE_FIELD_CHANGE: BASE_URL + '/use/v1/upd/singleField',
    // 批量复制用例
    BATCH_COPY_CASE: BASE_URL + '/use/v1/batch/copy/useCase',
    // 批量修改测试人
    BATCH_EDIT_TESTER: BASE_URL + '/use/v1/batch/upd/testUser',
    // 根据当前树id查父级名称组合
    QUERY_PARENT_NAME_BY_ID: BASE_URL + '/group/v1/query/parentGroupName',
    // 根据分组名查分组树
    QUERY_GROUP_LIST_BY_SEARCH: BASE_URL + '/group/v1/search/groups',
    // 引用
    REFERENCE_FROM_POOL_CASE: BASE_URL + '/pool/quote/case',
    // 复制
    COPY_FROM_POOL_CASE: BASE_URL + '/pool/copy/case',
    // 分组引用
    REFERENCE_GROUP_FROM_POOL: BASE_URL + '/pool/quote/group',
    // 复制分组
    GROUP_COPY: BASE_URL + '/group/v1/copy/group',
    // 重置分组
    RESET_GROUP_MODE: BASE_URL + '/group/v1/reset/groupIterate',
    // 用例排序
    USE_CASE_SORT: BASE_URL + '/use/v1/batch/order/useCase',
    // 分组排序
    GROUP_SORT_ITEM: BASE_URL + '/group/v1/order/group',
    //查询迭代下任务卡片
    QUERY_TASK_CARD: BASE_URL + '/bug/v1/find/task/list',
    //获取缺陷信息下拉列表
    QUERY_BUG_INFO: BASE_URL + '/bug/v1/find/base/list',
    //解除bug绑定
    REMOVE_BUG_RELATED: BASE_URL + '/bug/v1/remove/bug/relation',
    //生成链接跳转TB
    GET_TB_LINNK: BASE_URL + '/bug/v1/create/tb/link',
    //上传图片
    UPLOAD_IMAGE: BASE_URL + '/base/v1/upload/file',
    //创建缺陷卡片
    CREATED_BUG_TASK: BASE_URL + '/bug/v1/create/bug/task',
    //绑定/解绑atp用例与测试平台用例关联关系
    ADD_RELATION_ATP: BASE_URL + '/html/api/case/del/add/relation',
    IMPORT_EXCEL_OPEN: BASE_URL + '/import/excel/open',
    SEARCH_USER: BASE_URL + '/use/v1/search/user',
    IMPORT_OLD_CASE: BASE_URL + '/import/excel/case',
    DEVELOPER_SELF_TEST: BASE_URL + '/notify/v1/developer/self/test',
    SYNC_USE_TO_POOL: BASE_URL + '/use/v1/sync/use/to/pool',
    //编写完成
    EDIT_END: BASE_URL + '/flow/v1/write/case/complete',
    //查询测试流程设计
    QUERY_FLOW_LIST: BASE_URL + '/flow/v1/query/test/flow/list',
    //保存流程节点
    SAVE_TEST_FLOW: BASE_URL + '/flow/v1/save/test/flow',
    //删除流程节点
    DEL_EDIT_END: BASE_URL + '/flow/v1/del/test/flow',
    //推送钉钉消息推送流程消息给对应迭代参与人
    PUSH_FLOW_INFO: BASE_URL + '/flow/v1/push/flow/info',
    STEP_SIGN: BASE_URL + '/use/v1/upd/step/sign',
    //更新全局
    UPDATE_GLOBAL: BASE_URL + '/pool/group/update/global',
    //【测试用例】查询业务背景
    QUERY_BUSINESS_LIST: BASE_URL + '/use/ai/query/business/list',
    //【测试用例】保存业务背景
    SAVE_BUINESS_NEW: BASE_URL + '/use/ai/save/business',
    //【测试用例】AI生成用例
    AI_GENERATE_CASE: BASE_URL + '/use/ai/generate/case',
    //【测试用例】查询当前用户AI生成用例结果
    QUERY_CASE_RESULT: BASE_URL + '/use/ai/query/case/result',
    //【测试用例】AI用例保存为迭代用用例
    AI_CASE_SAVE: BASE_URL + '/use/ai/save/sprint/case',
    //【测试用例】查询上次选择的背景记录接口
    LAST_BUSINESS_CHOICE: BASE_URL + '/use/ai/last/business/choice',
    //【测试质量】新增测试质量数据
    ADD_TEST_QUALITY_DATA: BASE_URL + '/test/quality/add',
    //【测试质量】查询列表
    QUERY_TEST_QUALITY_LIST: BASE_URL + '/test/quality/query/img/list',
    //【测试质量】删除测试质量数据
    DEL_TEST_QUALITY_DATA: BASE_URL + '/test/quality/item/delete',
    //【测试质量】是否合规操作
    IS_COMPLIANCE_OPERATION: BASE_URL + '/test/quality/oper/pass',
    //【测试质量】获取迭代下的项目、上线分支、部署分支等信息
    GET_BRANCH_INFOS: BASE_URL + '/test/quality/getBranchInfos',
  },
  DEPLOY: {
    // 查询迭代部署任务节点数据
    QUERY_DEPLOY_NODE: BASE_URL + '/deploy/node/query',
    // 查询迭代项目数据
    QUERY_DEPLOY_PROJECT: BASE_URL + '/deploy/project/list',
    // 保存流程节点
    DEPLOY_NODE_SAVE: BASE_URL + '/deploy/node/save',
    // 执行部署
    DEPLOY_EXECUTE: BASE_URL + '/deploy/execute',
    // 获取部署包接口
    DEPLOY_PROJECT_PACKAGES: BASE_URL + '/deploy/project/packages',
    //查询团队配置
    QUERY_TEAM_CONFIG: BASE_URL + '/sprint/queryTeamConfigByKey'
  },
  ER: {
    //获取关联表接口
    DBDDIC_RELATION_TABLES: BASE_URL + '/dbdic/relation/tables',
    DBDESIGN_ER_DATA: BASE_URL + '/dbdesign/erdata'
  },
  TEST_PLAN: {
    SAVE_NEW_TEST_PLAN: BASE_URL + '/plan/v1/add/test/plan',
    QUERY_TEST_PLAN_LIST: BASE_URL + '/plan/v1/find/test/plan/list',
    QUERY_ENV_CONFIG_LIST: BASE_URL + '/plan/v1/find/env/list',
    TEST_PLAN_TOTAL: BASE_URL + '/plan/v1/count/test/plan',
    BATCH_MOVE_OUT_CASE: BASE_URL + '/plan/v1/batch/remove/case',
    // 修改预计完成时间
    MODIFY_PLAN_TIME: BASE_URL + '/plan/v1/update/plan/finish/time',
    // 规划用例列表
    BATCH_PLAN_USE_CASE: BASE_URL + '/plan/v1/batch/plan/case',
    // 测试计划执行用例
    EXECUTE_PLAN_CASE: BASE_URL + '/plan/v1/execute/case'
  },
  //配置平台
  CONFIG_MOUDLE: {
    SAVE_CASE: BASE_URL + '/config/v1/save/case/config',
    QUERY_CASE: BASE_URL + '/config/v1/query/case/config',
    ADD_MSG_PUSH_TEMP: BASE_URL + 'xxxxxx'
  },
  //UI自动化平台
  UI_ATUO_PLATFORM: {
    SAVE_GROUP: BASE_URL + '/ui/group/save/group',
    QUERY_LIST: BASE_URL + '/ui/group/query/list',
    DEL_GROUP: BASE_URL + '/ui/group/del/group',
    UPDATE_NAME: BASE_URL + '/ui/group/upd/name',
    MOVE_GROUP: BASE_URL + '/ui/group/move/group',
    UPDATE_ORDER: BASE_URL + '/ui/group/update/order',
    QUERY_CASE_LIST: BASE_URL + '/ui/case/query/list',
    DEL_CASE: BASE_URL + '/ui/case/del/case',
    UPDATE_LEVEL: BASE_URL + '/ui/case/update/level',
    UPDATE_ORDER_CASE: BASE_URL + '/ui/case/update/order',
    SAVE_CASE: BASE_URL + '/ui/case/save/case',
    DEFAULT_INFO: BASE_URL + '/ui/case/default/info',
    PARENT_NAME: BASE_URL + '/ui/group/query/parent/names',
    KEYWORD: BASE_URL + '/ui/keyword/save/keyword',
    SEARCH_GROUP: BASE_URL + '/ui/keyword/search/group',
    QUOTE_POOL_GROUP: BASE_URL + '/ui/keyword/quote/pool/group',
    DEFAULT_INFO_KEY: BASE_URL + '/ui/keyword/query/default/info',
    CONDITION_APOLLO: BASE_URL + '/ui/keyword/save/condition/apollo',
    QUERY_APOLLO: BASE_URL + '/ui/keyword/query/condition/apollo',
    CONDITION_DELETE: BASE_URL + '/ui/keyword/pub/condition/delete',
    CONDITION_DATABASE: BASE_URL + '/ui/keyword/query/condition/database',
    SAVE_CONDITION_DATABASE: BASE_URL + '/ui/keyword/save/condition/database',
    SAVE_CONDITION_REDIS: BASE_URL + '/ui/keyword/save/condition/redis',
    QUERY_CONDITION_REDIS: BASE_URL + '/ui/keyword/query/condition/redis',
    QUERY_KEYWORD_LIST: BASE_URL + '/ui/keyword/query/list',
    QUERY_BIND_CASE: BASE_URL + '/ui/keyword/query/bind/case',
    UN_BIND: BASE_URL + '/ui/keyword/batch/un/bind',
    BATCH_DELETE: BASE_URL + '/ui/keyword/batch/delete',
    BATCY_COPY_KEYWORD: BASE_URL + '/ui/keyword/batch/copy',
    KEYWORD_UPDATE_SORT: BASE_URL + '/ui/keyword/update/sort',
    POOL_CREATOR_LIST: BASE_URL + '/ui/keyword/pool/creator/list',
    PLAN_LIST: BASE_URL + '/ui/plan/query/plan/list',
    PLAN_ADD: BASE_URL + '/ui/plan/add',
    ADD_CASE: BASE_URL + '/ui/plan/add/case',
    EXE_COUNT: BASE_URL + '/ui/plan/test/exe/count',
    UPDATE_COMP_TIME: BASE_URL + '/ui/plan/upd/completion/time',
    REMOVE_CASE: BASE_URL + '/ui/plan/batch/remove/case',
    BATCH_EXE_CASE: BASE_URL + '/ui/exe/batch/exe/case',
    UPDATE_TESTER: BASE_URL + '/ui/plan/batch/upd/tester',
    CASE_INFO: BASE_URL + '/ui/case/query/case/info',
    REPORT_LIST: BASE_URL + '/ui/report/query/report/list',
    REPORT_DETAILS: BASE_URL + '/ui/report/query/report/details',
    KEYWORD_REPORT: BASE_URL + '/ui/report/query/keyword/report'
  },
  PUB_API: {
    JOIN_USER: BASE_URL + '/pub/api/sprint/join/user',// 迭代参与人
    USER_PM: BASE_URL + '/pub/api/sprint/user/pm',
    getToken: BASE_URL + '/pub/api/get/gateway/token',
    SQL_FORMAT: BASE_URL + '/pub/api/sql/format',
    SAFE_CREATE: BASE_URL + '/sprint/safeassess/create',
    SAFE_DETAILS: BASE_URL + '/sprint/safeassess/detail',
    UPDATE_SAFE: BASE_URL + '/sprint/safeassess/report',
    DOWNLOAD_URL: BASE_URL + '/sprint/safeassess/authOss'
  },
  // oss上传静态资源接口调用
  STATIC_RESOURCE: {
    UPLOAD_API: BASE_URL + '/oss/file/upload',
    BUCKETDIR_API: BASE_URL + '/oss/bucketDir/list',
    OSSLIST_API: BASE_URL + '/oss/file/list',
  },
  // 组件治理接口调用
  COMPONENT_GOVERNANCE: {
    // 查询组件列表
    QUERY_COMPONENT_LIST: BASE_URL + '/logory/sdk/list',
    DELETE_COMPONENT_LIST: BASE_URL + '/logory/sdk/delete',
    ADD_OR_UPDATE_COMPONENT: BASE_URL + '/logory/sdk/save',
    GET_DETAIL_COMPONENT_WORD_VERSION: BASE_URL + '/logory/sdk/view',
    ADD_OR_UPDATE_WORD: BASE_URL + '/logory/sdk/docs/save',
    GET_WORD_EXCEL_PDF_PREVIEW: BASE_URL + '/wps/office/onlineWpsFile',
    GET_WHITE_BOOK_PREVIEW: BASE_URL + '/logory/sdk/whitebook/preview',
    ADD_OR_UPDATE_VERSION: BASE_URL + '/logory/sdk/version/save',
    GET_WORD_DOWNLOAD_URL: BASE_URL + '/logory/sdk/doc/download',
    SDK_TXT_EDIT: BASE_URL + '/logory/sdk/txt/edit'
  },
  // 前置评估
  QZPG: {
    SAVE_REPORT: BASE_URL + '/assess/v1/add/or/edit/report',
    GET_REPORT: BASE_URL + '/assess/v1/query/report/info',
    SAVE_FUNCTION: BASE_URL + '/assess/v1/add/or/edit/function',
    DEL_FUNCTION: BASE_URL + '/assess/v1/delete/function',
    SAVE_PLAN_RESULT: BASE_URL + '/assess/v1/save/plan/result'
  },
  //需求管理
  DEMAND_MANAGE: {
    QUERY_LIST: BASE_URL + '/demand/v1/query/list',
    CASCADE_SAVE: BASE_URL + '/demand/v1/cascade/save',
    CASCADE_LIST: BASE_URL + '/demand/v1/cascade/list',
    CASCADE_INFO: BASE_URL + '/demand/v1/query/cascade/info',
    QUERY_SYSTEM_LIST: BASE_URL + '/demand/v1/query/system', // 产品系统列表
    PRODUCT_SWITCH: BASE_URL + '/demand/v1/product/switch', // 产品切换
    QUERY_DEMAND_DETAIL: BASE_URL + '/demand/v1/query/details', // 需求迭代详情
    SAVE_DEMAND: BASE_URL + '/demand/v1/save', // 保存需求迭代
    QUERY_ISBIND: BASE_URL + '/demand/v1/module/hasBind', // 查询模块是否绑定功能
    DEL_DEMAND_CASCADE: BASE_URL + '/demand/v1/cascade/del', // 删除模块，功能
    EXC_DEMAND_LIST: BASE_URL + '/exc/demand/query/exc/demand/list', //查询异常需求列表
    SAVE_REASON: BASE_URL + '/exc/demand/save/demand/reason', //【需求迭代】保存异常原因
    QUERY_PROJECT_TOGGLE: BASE_URL + '/demand/v1/query/project/toggle', //查询项目下的功能开关数据
    RELATE_TOGGLE: BASE_URL + '/demand/v1/demand/relate/toggle', //【需求-功能开关】需求关联功能开关
    ADD_FEATURE_TOGGLE: BASE_URL + '/demand/v1/add/feature/toggle', // 创建功能开关
    FEATURE_TOGGLE_LIST: BASE_URL + '/demand/v1/query/demand/toggle/list', // 功能开关列表
    QUERY_PUBLISH_FEATURE_TOGGLE: BASE_URL + '/demand/v1/query/demand/publish/toggle', // 发布功能开关前查询
    PUBLISH_FEATURE_TOGGLE: BASE_URL + '/demand/v1/publish/feature/toggle', // 发布功能开关
    QUERY_EXC_TEAM_LIST: BASE_URL + '/exc/demand/query/exc/team/list', // 查询异常需求列表
    QUERY_DEMAND_UI_USER: BASE_URL + '/demand/draw/query/demand/ui', // 查询需求效果图设计人员
    SAVE_DEMAND_UI_USER: BASE_URL + '/demand/draw/save/demand/ui', // 保存需求效果图设计人员
    SAVE_DEMAND_FRONT: BASE_URL +  '/demand/save/demandIdentify/front', //保存需求策略时前置处理
    UPDATE_CUSTOMER_PROJECT: BASE_URL + '/demand/update/customer/project' // 修改客户信息项目Id
  },
  // 三方组件
  SANFANG_COMPONENT: {
    QUERY_COMPONENT_LIST: BASE_URL + '/dependency/stable/list',
    ADDOREDIT_COMPONENT: BASE_URL + '/dependency/stable/addOrEdit',
    DELETE_COMPONENT: BASE_URL + '/dependency/stable/delete'
  },
  CMBD: {
    ADD_WH_TEAM: BASE_URL + '/pub/api/cmdb/project/addWhTeam',
  },
  //版本发布
  RELEASE: {
    SAVE_RELEASE_LIST: BASE_URL + '/ddcs/v1/batch/FunctionPointVer', //保存批量发布功能点版本接口
    PUSH_FUNC_VER: BASE_URL + '/ddcs/v1/push/FunctionPointVer',   //推送功能点版本接口
    QUERY_RELEASE_RECORD: BASE_URL + '/ddcs/requirement/v1/query/requirement/list', //分页查询需求发布记录
    UPDATE_RELEASE_RECORD: BASE_URL + '/ddcs/requirement/v1/update/requirement', //修改需求发布记录
    QUERY_FUNCTION_LIST: BASE_URL + '/ddcs/v1/query/functionPoint/list', // 查询功能点
    UPDATE_FUNCTION_POINT: BASE_URL + '/ddcs/v1/update/functionPoint', // 修改功能点
    ADD_FUNCTION_POINT: BASE_URL + '/ddcs/v1/add/functionPoint', // 新增功能点
    ADD_RELEASE: BASE_URL + '/ddcs/v1/add/functionPointVer', // 新增功能点版本
    UPDATE_RELEASE: BASE_URL + '/ddcs/v1/update/functionPointVer', // 修改功能点版本
    QUERY_RELEASE_LIST: BASE_URL + '/ddcs/v1/query/functionpointversion/list', //查询功能点版本待发布已发布列表接口
    DELETE_FUNCTION_POINT: BASE_URL + '/ddcs/v1/delete/functionPoint', // 删除功能点
    DELETE_RELEASE: BASE_URL + '/ddcs/v1/delete/functionPointVer', // 删除功能点版本
    QUERY_OPERATION_LINK_LIST: BASE_URL + '/ddcs/operationlink/v1/page/operationLink', // 分页查询操作链路列表
    UPDATE_OPERATION_LINK: BASE_URL + '/ddcs/operationlink/v1/update/operationLink', // 修改操作链路
    ADD_OPERATION_LINK: BASE_URL + '/ddcs/operationlink/v1/add/operationLink', // 新增操作链路
    DELETE_OPERATION_LINK: BASE_URL + '/ddcs/operationlink/v1/delete/operationLink', // 删除操作链路
    QUERY_FUNCTION_MODULE_LIST: BASE_URL + '/ddcs/v1/module/list/functionModule', // 查询功能模块列表
    ADD_FUNCTION_MODULE: BASE_URL + '/ddcs/v1/module/add/functionModule', // 新增功能模块
    DELETE_FUNCTION_MODULE: BASE_URL + '/ddcs/v1/module/delete/functionModule', // 删除功能模块
    QUERY_SKU_LIST: BASE_URL + '/ddcs/v1/sku/list/sku', // 查询SKU列表
    ADD_SKU: BASE_URL + '/ddcs/v1/sku/add/sku', // 新增SKU
    DELETE_SKU: BASE_URL + '/ddcs/v1/sku/delete/sku', // 删除SKU
    QUERY_PRODUCT_LINE_LIST: BASE_URL + '/ddcs/v1/productLine/list/productLine', // 查询产品线列表
    ADD_PRODUCT_LINE: BASE_URL + '/ddcs/v1/productLine/add/productLine', // 新增产品线
    QUERY_USER_SCENARIO_SOLU_LIST: BASE_URL + '/ddcs/v1/userscenariosolu/list/userscenariosolu', // 查询用户工作场景解决方案列表
    ADD_USER_SCENARIO_SOLU: BASE_URL + '/ddcs/v1/userscenariosolu/add/userscenariosolu', // 新增用户工作场景解决方案
    DELETE_USER_SCENARIO_SOLU: BASE_URL + '/ddcs/v1/userscenariosolu/delete/userscenariosolu', // 删除用户工作场景解决方案
    QUERY_FUNCTION_POINT_BURYS: BASE_URL + '/ddcs/v1/query/functionPointBurys', // 查询功能点埋点列表
    QUERY_PRODUCT_PORT_LIST: BASE_URL + '/ddcs/v1/productPort/queryProductPortPage', // 分页查询产品端列表
    ADD_PRODUCT_PORT: BASE_URL + '/ddcs/v1/productPort/addProductPort', // 新增产品端口
    UPDATE_PRODUCT_PORT: BASE_URL + '/ddcs/v1/productPort/updateProductPort', // 修改产品端口
    DELETE_PRODUCT_PORT: BASE_URL + '/ddcs/v1/productPort/deleteProductPort', // 删除产品端口
  },
  TOOL_MSG: {
    QUERY_USER_BY_NAME: BASE_URL + '/message/tool/userInfo/query', // 查询钉钉用户
    QUERY_ALL_DEPT_LIST: BASE_URL + '/message/tool/msg/deptInfo/query', // 查询钉钉组织架构，所有层级的部门
    QUERY_DINGDING_GROUP_LIST: BASE_URL + '/message/tool/dding/group/query', // 查询钉钉群
    QUERY_DINGDING_GROUP_USER_LIST: BASE_URL + '/message/tool/dding/user/at/query', // 群用户
    QUERY_TOOL_MSG_CONFIG: BASE_URL + '/message/tool/msg/config', // 消息配置
    PUSH_MSG: BASE_URL + '/message/tool/msg/handle', // 发送消息
    QUERY_PUSH_MSG_RECORD_LIST: BASE_URL + '/message/tool/msg/push/record', // 消息记录
    UPLOAD_RECEIVER: BASE_URL + '/message/tool/msg/receiver/upload',
    QUERY_MORE_USER: BASE_URL + '/message/tool/msg/push/record/receiver',
    MSG_RECORD_DETAIL: BASE_URL + '/message/tool/msg/push/record/details'
  },
  DEMAND_RECOGNITION: {
    QUERY_DEMAND_RECOGNITION: BASE_URL + '/demand/query/demandInfo', // 查询需求识别
    SAVE_DEMAND_RECOGNITION: BASE_URL + '/demand/save/demandIdentify', // 保存需求识别
    QUERY_VALUE_TEMPLATE: BASE_URL + '/demand/query/valueTemplate', // 查询价值模板
    QUERY_HISTORY_VALUE_TEMPLATES: BASE_URL + '/demand/query/other/valueTemplate', // 查询历史价值模板
    SAVE_VALUE_TEMPLATE: BASE_URL + '/demand/save/valueTemplate', // 保存价值模板
  }
};

export { REQ_CONST_URL };
