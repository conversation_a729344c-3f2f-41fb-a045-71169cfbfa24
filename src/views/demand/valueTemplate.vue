<template>
    <div class="value-template">
    <div class="value-template-header">
      <div>
        <div class="value-template-header_left-back" @click="goBack">
          <svg class="icon" style="width: 16px; height: 16px" aria-hidden="true">
            <use xlink:href="#icon-a-ziyuan20"></use>
          </svg>
        </div>
      </div>
      <div class="value-template-header_right">
          <div class="value-template-header-title-button">
            <div class="value-template-header-title" :title="demandInfo.taskName">
              <span>{{ demandInfo.taskName }}</span>
            </div>
            <div class="value-template-header-button">
              <el-button @click="goDemandDocument" data-lgid="btn8" :disabled="!demandInfo.requirementUrl">需求文档</el-button>
              <el-button @click="goOperationTask"  data-lgid="btn9">运营任务</el-button>
              <el-button type="primary" @click="goTbCard"  data-lgid="btn10">TB卡片</el-button>
            </div>
          </div>
          <div class="value-template-header-customerUserProblem">
            <span style="font-weight: 600;">客户/用户问题：</span>
            <div class="multiline-ellipsis" :title="demandInfo.customerUserProblem">
              {{ demandInfo.customerUserProblem || '暂无客户问题描述' }}
            </div>
          </div>
      </div>
    </div>
    <div class="value-template-content">
      <div class="value-template-content-left">
        <demand-info :demandInfo="demandInfo" :customerInfo="customerInfo" @refresh-data="queryValueTemplateInfo"></demand-info>
      </div>
      <div class="value-template-content-right">
        <value-template-component
          :templateInfo="templateInfo"
          :demandInfo="demandInfo"
          :taskId="taskId"
          @refresh-data="queryValueTemplateInfo"></value-template-component>
      </div>
    </div>
    </div>
</template>

<script>
import valueTemplateComponent from '@/components/demand/valueTemplate.vue'
import demandInfo from '@/components/demand/info.vue'
import { queryValueTemplateInfo } from '@/assets/api/api_demand_recognition'

export default {
  components: {
    valueTemplateComponent,
    demandInfo
  },
  data() {
    return {
      demandInfo: {},
      customerInfo: {},
      templateInfo: {
        id: '',
        proposer: '',
        productManager: '',
        demandName: '',
        user: '',
        associatedProducts: '',
        workloadEstimate: '',
        customerUserDemand: '',
        workScenario: '',
        correspondingProblem: '',
        currentHandlingMethod: '',
        solutionOverview: '',
        valueToCustomer: '',
        valueToUser: '',
        firstKeyIndicator: '',
        promotionMethod: '',
        promotionPlan: '',
        reviewer: '',
        reviewerId: '',
        defaultReviewerId: '',
        reviewCount: 0
      },

      taskId: '',
      processId: '',
      timer: null
    }
  },
  created() {
    this.taskId = this.$route.query.taskId
    this.processId = this.$route.query.processId
  },
  mounted() {
    this.queryValueTemplateInfo()
  },
  beforeDestroy() {
    // 组件销毁前清除定时器
    if (this.timer) {
      clearTimeout(this.timer)
      this.timer = null
    }
  },
  methods: {
    goBack() {
      //返回工作平台
        this.$router.push({
            path: '/dashBoard/workingPlatform'
        })
    },
    queryValueTemplateInfo () {
      let loading = this.$loading({
          lock: true,
          text: '加载中',
          background: 'rgba(0, 0, 0, 0.7)'
        })

      // 埋点手动上报结果
      const _config = {
        eventType: 'click',
        resourceId: 'btn102',
        resourceName: '价值模板',
        eventDesc: '点击',
        ext: {
          taskId: this.taskId
        }
      }
      window._$lgFemSdk && window._$lgFemSdk.pageClickEvent(_config)
      queryValueTemplateInfo({
        taskId: this.taskId
      }).then(res => {
        // 统一处理API响应格式，兼容 res.data.reCode 和 res.reCode 两种格式
        const responseData = res.data || res;
        const reCode = responseData.reCode;

        if (reCode === '0' || reCode === 0) {
          const result = responseData.result || {};

          // 确保数据存在后再赋值
          if (result.demandInfo) {
            this.demandInfo = result.demandInfo;
          }
          if (result.customerInfo) {
            this.customerInfo = result.customerInfo;
          }
          if (result.templateInfo) {
            this.templateInfo = result.templateInfo;
          }
          // 触发一个额外的事件，以便于组件强制更新
          this.$nextTick(() => {
            this.$forceUpdate();
          });
        } else {
          console.error('API请求失败:', responseData.reInfo || '未知错误');
        }
      }).catch(err => {
      }).finally(() => {
        if (loading) {
          loading.close()
        }
      });
    },
    goDemandDocument () {
      if (this.demandInfo.requirementUrl) {
        window.open(this.demandInfo.requirementUrl, '_blank')
      } else {
        this.$message.warning('暂无需求文档')
      }
    },
    goOperationTask () {
      if (this.demandInfo.operationUrl) {
        window.open(this.demandInfo.operationUrl, '_blank')
      } else {
        this.$message.warning('暂无运营任务')
      }
    },
    goTbCard() {
      window.open(this.demandInfo.tbUrl, '_blank')
    }
  }
}
</script>

<style lang="scss" scoped>
.value-template {
    height: calc(100vh - 100px);
    padding: 20px;
    background-color: #f0f2f5;

    .value-template-header {
      max-width: 1680px;
      margin: 0 auto;
      display: flex;
      border-radius: 4px;
      padding: 30px;
      background-color: #fff;

      .value-template-header_left-back {
        width: 36px;
        height: 36px;
        box-shadow: 0px 2px 5px 0px rgba(128, 128, 128, 50%), 0px 0px 10px 0px #dadce6 inset, 0px 0px 3px 0px #dadce6 inset;
        border-radius: 50%;
        margin-right: 10px;
        background-color: #fff;
        text-align: center;
        line-height: 32px;
        color: #2f72f2;
        cursor: pointer;
        transform: rotate(180deg);
        display: flex;
        align-items: center;
        justify-content: center;

        i {
          font-size: 16px;
          color: #45484C;
        }

        &:hover {
          background-color: #E6E6E6;
          box-shadow: 0px 2px 5px 0px rgba(128, 128, 128, 0.5), 0px 0px 10px 0px #DADCE6 inset, 0px 0px 3px 0px #DADCE6 inset
        }
      }

      .value-template-header_right {
        width: calc(100% - 36px);
      }

      .value-template-header-title-button {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .value-template-header-customerUserProblem {
        margin-top: 10px;
        height: 42px;
        font-size: 14px;
        color: #333;
        display: flex;

        .multiline-ellipsis {
          display: -webkit-box;
          -webkit-line-clamp: 2; /* 显示两行 */
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: calc(100% - 110px);
          line-height: 1.5;
        }
      }

      .value-template-header-title {
        font-size: 18px;
        font-weight: 600;
        width: 50%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width: 100%;
      }

      .value-template-header-button {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      @media screen and (max-width: 1200px) {
        height: auto;
        padding: 20px;
        .value-template-header-title {
          margin-bottom: 15px;
        }
      }
      @media screen and (max-width: 768px) {
        .value-template-header-title {
          font-size: 18px;
        }
        .value-template-header-button {
          flex-wrap: wrap;
          gap: 10px;
        }
      }
    }

    .value-template-content {
      display: flex;
      justify-content: space-between;
      max-width: 1740px;
      margin: 0 auto;
      margin-top: 20px;

      .value-template-content-left {
        width: 636px;
        height: calc(100vh - 250px);
        background-color: #fff;
        margin-right: 24px;
        border-radius: 4px;
        overflow-y: hidden;
      }

      .value-template-content-right {
        width: calc(100% - 636px);
        height: calc(100vh - 250px);
        background-color: #fff;
        border-radius: 4px;
        overflow-y: hidden;
      }

      @media screen and (max-width: 1440px) {
        .value-template-content-left {
          width: 500px;
        }
        .value-template-content-right {
          width: calc(100% - 524px);
        }
      }

      @media screen and (max-width: 1200px) {
        .value-template-content-left {
          width: 400px;
        }
        .value-template-content-right {
          width: calc(100% - 424px);
        }
      }

      @media screen and (max-width: 992px) {
        flex-direction: column;
        .value-template-content-left {
          width: 100%;
          height: auto;
          min-height: 300px;
          margin-right: 0;
          margin-bottom: 20px;
        }
        .value-template-content-right {
          width: 100%;
          height: auto;
          min-height: calc(100vh - 600px);
        }
      }

      @media screen and (max-width: 768px) {
        margin-top: 15px;
        .value-template-content-left,
        .value-template-content-right {
          padding: 15px;
        }
      }
    }
        ::v-deep .el-textarea__inner {
        padding-bottom: 28px;
        font-size: 16px;
        font-family: 'PingFang SC';
        padding-right: 50px;
    }
}
</style>