<template>
  <div class="demand-info">
    <div class="demand-info-title">
      <el-tabs v-model="activeName" @tab-click="handleClick" class="demand-tabs">
        <el-tab-pane label="需求信息" name="first">
          <div class="tab-content-scroll">
            <el-form :model="demandInfo" label-width="120px" label-position="left">
              <el-form-item label="执行者">
                <span>{{ demandInfo.executor }}</span>
              </el-form-item>
              <el-form-item label="提出人">
                <span>{{ demandInfo.proposer }}</span>
              </el-form-item>
              <el-form-item label="参与者">
                <span>{{ demandInfo.participants }}</span>
              </el-form-item>
              <el-form-item label="创建时间">
                <span>{{ formatDateTime(demandInfo.createdTime) }}</span>
              </el-form-item>
              <el-form-item label="需求状态">
                <span>{{ demandInfo.status }}</span>
              </el-form-item>
              <el-form-item label="产品线">
                <span>{{ demandInfo.productLine }}</span>
              </el-form-item>
              <el-form-item label="SKU">
                <span>{{ demandInfo.sku }}</span>
              </el-form-item>
              <el-form-item label="专题">
                <span>{{ demandInfo.topic }}</span>
              </el-form-item>
              <el-form-item label="优先级">
                <span>{{ demandInfo.priority }}</span>
              </el-form-item>
              <el-form-item label="产品策略">
                <span>{{ demandInfo.productStrategy }}</span>
              </el-form-item>
              <el-form-item label="判断依据">
                <span>{{ demandInfo.judgmentBasis }}</span>
              </el-form-item>
              <el-form-item label="反馈信息">
                <span>{{ demandInfo.feedBack }}</span>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>
        <el-tab-pane label="客户信息" name="second">
          <div class="tab-content-scroll">
            <el-form :model="customerInfo" label-width="140px" label-position="left">
                <el-form-item label="项目名称">
                    <el-select remote filterable :remote-method="(query) => searchProject(query, 'name')"  placeholder="请输入项目名称" v-model="customerInfo.orgName" style="width:90%" @change="(value) => handleProjectChange(value, 'name')" value-key="orgName">
                        <el-option v-for="item in projectList" :key="item.orgId" :label="item.orgName" :value="item.orgName"></el-option>
                    </el-select>
                </el-form-item>
              <el-form-item label="项目ID">
                    <el-select remote filterable :remote-method="(query) => searchProject(query, 'id')" placeholder="请输入项目ID" v-model="customerInfo.orgId" style="width:90%"  @change="(value) => handleProjectChange(value, 'id')" value-key="orgId">
                        <el-option v-for="item in projectList" :key="item.orgId" :label="item.orgId" :value="item.orgId"></el-option>
                    </el-select>
              </el-form-item>
              <el-form-item label="客户名称">
                <span>{{ customerInfo.companyName }}</span>
              </el-form-item>
              <el-form-item label="项目人员">
                <table class="person-table">
                  <tr>
                    <td v-if="customerInfo.saleName">营销</td>
                    <td v-if="customerInfo.implementOperate">交付</td>
                    <td v-if="customerInfo.operateName">运营</td>
                  </tr>
                  <tr>
                    <td v-if="customerInfo.saleName">{{ customerInfo.saleName }}</td>
                    <td v-if="customerInfo.implementOperate">{{ customerInfo.implementOperate }}</td>
                    <td v-if="customerInfo.operateName">{{ customerInfo.operateName }}</td>
                  </tr>
                </table>
              </el-form-item>
              <el-form-item label="是否数据服务客户">
                <span>{{ customerInfo.isDataServiceCustomer == 1 ? '是' : customerInfo.isDataServiceCustomer == 0 ? '否' : '' }}</span>
              </el-form-item>
              <el-form-item label="数据服务费收取">
                <span>{{ customerInfo.sjfwf || '' }}</span>
              </el-form-item>
              <el-form-item label="交付阶段">
                <span>{{ customerInfo.jfjd || '' }}</span>
              </el-form-item>
              <el-form-item label="交付SKU">
                <span>{{ customerInfo.jfsqSku || '' }}</span>
              </el-form-item>
              <el-form-item label="配置SKU">
                <span class="multi-line-text">{{ customerInfo.ldSku || '' }}</span>
              </el-form-item>
              <el-form-item label="是否DIO客户">
                <span>{{ customerInfo.isDioCustomer == 1 ? '是' : customerInfo.isDioCustomer == 0 ? '否' : '' }}</span>
              </el-form-item>
              <el-form-item label="上月业务体量">
                <span v-if="customerInfo.businessRevenueSd">{{ customerInfo.businessRevenueSd + '元' }}</span>
              </el-form-item>
              <el-form-item label="上月一次支付成功率">
                <span>{{ customerInfo.onetimeAppPayApproveRateSd? formatPercent(customerInfo.onetimeAppPayApproveRateSd) : '' }}</span>
              </el-form-item>
              <el-form-item label="上月项目PH值">
                <span>{{ customerInfo.phscoreCm }}</span>
              </el-form-item>
              <el-form-item label="上月项目PH值详情">
                <span @click="openPhscoreCmUrl" data-lgid="btn109-20250723154110" style="color: #007bff;cursor: pointer;">{{ customerInfo.phscoreCmUrl }}</span>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import {queryProjectList} from '@/assets/js/moneyRequest.js'
import { updateCustomerProject } from '@/assets/api/api_demand.js'
import { debounce } from "lodash"
    // 修正：确保在组件实例中正确引用 this
   let _this
export default {
  data() {
    return {
        activeName: 'first',
        projectList: [] // 项目下拉列表
    }
  },
  props: {
    demandInfo: {
      type: Object,
      default: () => ({})
    },
    customerInfo: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    hasData() {
      return Object.keys(this.demandInfo).length > 0
    }
  },
  created () {
  },
  mounted () {
    _this = this
  },
  methods: {
    searchProject: debounce((query, type) => {
        if (!query) {
          _this.projectList = [];
            return;
        }
        // ID类型验证：只允许输入数字
        if (type === 'id' && !/^\d+$/.test(query)) {
          _this.$message.warning('项目ID只能输入数字');
          _this.projectList = [];
            return;
        }
        const searchParams = type === 'id' ? { orgId: query } : { orgLikeName: query };

        queryProjectList(searchParams).then(res => {
            if (res.data.reCode == '0') {
              _this.projectList = res.data.result.orgList || [];
            }
        });
    }, 500),

    // 统一的项目选择处理
    handleProjectChange(value, type) {
      if (!value) return;
      // 根据选择类型查找匹配的项目
      const matchedProject = this.projectList.find(item => type === 'name' ? item.orgName === value : item.orgId === value);
      if (!matchedProject) return;
      // 更新表单数据
      this.customerInfo.orgId = matchedProject.orgId;
      this.customerInfo.orgName = matchedProject.orgName;
      // 埋点手动上报结果
      const _config = {
        eventType: 'click',
        resourceId: 'btn172-20250723155933',
        resourceName: '修改项目',
        eventDesc: '点击',
        ext: {
          taskId: this.demandInfo.taskId,
          projectId: matchedProject.orgId,
          projectName: matchedProject.orgName
        }
      };
      window._$lgFemSdk && window._$lgFemSdk.pageClickEvent(_config);
      updateCustomerProject({
          taskId: this.demandInfo.taskId,
          projectId: matchedProject.orgId,
          projectName: matchedProject.orgName
      }).then(res => {
          if (res.data.reCode == '0') {
              // 埋点手动上报结果
              const _config = {
                eventType: 'click',
                resourceId: 'btn190-20250723160036',
                resourceName: '修改项目成功',
                eventDesc: '点击',
                ext: {
                  projectId: matchedProject.orgId,
                  projectName: matchedProject.orgName,
                  taskId: this.demandInfo.taskId
                }
              }
              window._$lgFemSdk && window._$lgFemSdk.pageClickEvent(_config)
                this.$message.success('修改成功');
                this.$emit('refresh-data');
            }
        });
    },
    handleClick (tab, event) {
    },
    formatDateTime(datetime) {
      if (!datetime) return '';

      // 处理形如 2025-04-15 21:04:41.0 的日期时间格式
      try {
        // 移除可能存在的毫秒部分
        const cleanTime = datetime.replace(/\.\d+$/, '');
        const date = new Date(cleanTime.replace(/-/g, '/'));

        if (isNaN(date.getTime())) return datetime;

        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');
        const hours = date.getHours().toString().padStart(2, '0');
        const minutes = date.getMinutes().toString().padStart(2, '0');
        const seconds = date.getSeconds().toString().padStart(2, '0');

        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      } catch (error) {
        return datetime;
      }
    },
    formatPercent(value) {
      if (!value) return '';

      // 将字符串转换为数字
      const num = parseFloat(value);
      if (isNaN(num)) return value;

      // 如果已经是百分比格式（包含%），直接返回
      if (typeof value === 'string' && value.includes('%')) return value;

      // 如果是小数（如0.85），则乘以100并加上百分号
      if (num <= 1) {
        return (num * 100).toFixed(2) + '%';
      }

      // 如果已经是整数形式（如85），则直接加上百分号
      return num.toFixed(2) + '%';
      },
    openPhscoreCmUrl() {
      window.open(this.customerInfo.phscoreCmUrl, '_blank');
    }
  }
}
</script>

<style lang="scss" scoped>
.demand-info {
  height: 100%;
  padding: 24px 30px;

  .demand-info-title {
    height: 100%;
    font-size: 14px;
    font-weight: 600;
    display: flex;
    flex-direction: column;
  }

  .demand-tabs {
    display: flex;
    flex-direction: column;
    height: 100%;

    ::v-deep .el-tabs__header {
      flex-shrink: 0; /* 防止头部被压缩 */
      margin-bottom: 0;
      padding: 10px 0 0;
      z-index: 1; /* 确保头部在内容之上 */
    }

    ::v-deep .el-tabs__content {
      flex: 1;
      overflow: hidden; /* 防止出现外部滚动条 */
      height: calc(100% - 45px); /* 减去tab头部高度 */
      position: relative;
    }

    ::v-deep .el-tab-pane {
      height: 100%;
      overflow: hidden;
    }
  }

  .tab-content-scroll {
    height: 100%;
    overflow-y: auto;
    padding: 15px 0;
    box-sizing: border-box;
  }
  .multi-line-text {
    white-space: pre-line;
  }
}
.person-table {
  border-collapse: collapse;
  margin: 0;
  td {
    border: 1px solid #ccc;
    padding: 2px 8px;
    text-align: center;
    font-size: 14px;
    min-width: 40px;
    vertical-align: middle;
  }
}
</style>
