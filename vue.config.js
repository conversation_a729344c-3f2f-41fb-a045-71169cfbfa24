const isDevelopment = process.env.VUE_APP_ENV === 'development'
const path = require('path')
const MiniCssExtractPlugin = require('mini-css-extract-plugin')
const SpeedMeasureWebpackPlugin = require('speed-measure-webpack-plugin')
const UploadSourceMapPlugin = require("lg-upload-sourcemap-webpack-plugin");
const CopyWebpackPlugin = require('copy-webpack-plugin');
const WTYT_CDN_ENV = process.env.WTYT_CDN_ENV
  ? process.env.WTYT_CDN_ENV
  : process.env.VUE_APP_ENV === "testing"
    ? "//test-imgt.log56.com"
    : process.env.VUE_APP_ENV === "prepublish"
      ? "//pre-imgt.log56.com"
      : "//imgt.log56.com";

function resolve (dir) {
  return path.join(__dirname, dir)
}
const currentEnv = process.env.VUE_APP_ENV
let formatEnv
switch (currentEnv) {
  case 'development':
    formatEnv = 'test'
    break;
  case 'testing':
    formatEnv = 'test'
    break;
  case 'staging':
    formatEnv = 'preprod'
    break;
  default:
    formatEnv = 'prod'
    break
}
const isTestEnv = formatEnv === "test"
module.exports = {
  lintOnSave: false,
  crossorigin: 'anonymous',
  productionSourceMap: isTestEnv,
  configureWebpack: (config) => {
    if (isTestEnv) {
      // config.devtool = 'eval-source-map'
      // 这里现阶段只在cicd打包的测试环境下上传，如果环境判断不一致，根据自己的项目调整
      const UploadSourceMapPlugin = require("lg-upload-sourcemap-webpack-plugin");
      const env =
        isTestEnv
          ? "fat"
          : process.env.VUE_APP_ENV === "prepublish"
            ? "uat"
            : "pro";
      config.plugins.push(
        new UploadSourceMapPlugin({
          project: 'eagle_system_h5', // 埋点初始化中的projectNo字段的值
          env,
        })
      )
    }
  },
  css: {
    //查看CSS属于哪个css文件
    sourceMap: true,
  },
  publicPath: isDevelopment ? '/' : process.env.lg_rid ? `${WTYT_CDN_ENV}/eagle_system_h5/${process.env.lg_rid}/` : `${WTYT_CDN_ENV}/eagle_system_h5/`,
  outputDir: process.env.lg_rid ? `eagle_system_h5/${process.env.lg_rid}` : 'eagle_system_h5',
  assetsDir: 'static',
  // productionSourceMap: false,
  chainWebpack: config => {
    config.plugin('speed')
      .use(SpeedMeasureWebpackPlugin)

    config.resolve.alias.set('@', resolve('src'));
    config.output.crossOriginLoading('anonymous')
    /**
   * 删除懒加载模块的 prefetch preload，降低带宽压力
   */
    config.plugins.delete('prefetch').delete('preload')
    config.resolve.symlinks(true)
    // config.output.filename('js/[name].js').end();
    // config.output.chunkFilename('js/[name].js').end();
  },
  devServer: {
    port: 8088,
    headers: {
      'Access-Control-Allow-Origin': '*',
    },
    proxy: {
      '/api': {
        target: 'http://test-eagle.log56.com/', // 测试环境
        // target: 'http://192.168.217.20:27250/', // 郭灿
        // target: 'http://pre-eagle.log56.com/', // 预发布
        // target: 'http://192.168.217.17:27250',  // 韩真真
        // target: 'http://192.168.211.11:27250',  // 韩真真
        // target: 'http://192.168.211.46:27250',    // 邱强
        pathRewrite: {
          '^/api': ''
        },
        bypass: function (req, res, proxyOptions) {
          req.headers.cookie = req.headers.cookie + ';SESSION=NGZkNDkzYWUtY2M3OS00N2E4LWE1MjItN2JiNDdjNDA3ODQ3;'
        }
      },
      '/microservice': {
        target: 'https://test-microservice.log56.com/luge/gateway.do',
        changeOrigin: true,
        pathRewrite: {
          '^/microservice': ''
        },
        bypass: function (req, res, proxyOptions) {
          req.headers.cookie = req.headers.cookie + ';SESSION=Mzk1ODIwMjMtMWM3ZS00OGM4LTk0NDgtZGRlZGNhZmZiNGY5;'
        }
      },
      '/safetyApi': {
        target: 'http://test-microservice.log56.com/luge/gateway.do',
        changeOrigin: true,
        pathRewrite: {
          '^/safetyApi': ''
        }
      },
      '/syApi': {
        target: 'https://test-os-gateway.log56.com',
        changeOrigin: true,
        pathRewrite: {
          '^/syApi': ''
        }
      },
      '/exportApi': {
        target: 'http://test-microservice.log56.com/luge/gateway.do',
        changeOrigin: true,
        pathRewrite: {
          '^/exportApi': ''
        }
      }
    }
  }
}
